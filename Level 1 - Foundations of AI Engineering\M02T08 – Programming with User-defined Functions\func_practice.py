"""
Function Practice - Completing the Course Task

This program demonstrates creating and using a user-defined function
as requested in the course materials.

Task: Create a function called 'add_three' that takes three parameters
and returns their sum.
"""


def add_three(num1, num2, num3):
    """
    Add three numbers together and return the result.
    
    Parameters:
    num1 (int/float): First number
    num2 (int/float): Second number  
    num3 (int/float): Third number
    
    Returns:
    int/float: Sum of all three numbers
    """
    # Create a new variable y that is the sum of all three numbers
    y = num1 + num2 + num3
    
    # Return the result
    return y


# Test the function with the specified numbers: 52, 25, and 1403
print("Testing the add_three function:")
print("=" * 40)

# Call the function and store the result in sum_func
sum_func = add_three(52, 25, 1403)

# Print the result (converting to string for concatenation)
print("The sum of 52, 25, and 1403 is: " + str(sum_func))

# Alternative way to print (using f-strings - more modern)
print(f"Using f-string: The sum is {sum_func}")

# Let's test with some other numbers too
print("\nTesting with other numbers:")
print("=" * 40)

test1 = add_three(10, 20, 30)
print(f"10 + 20 + 30 = {test1}")

test2 = add_three(1.5, 2.5, 3.0)
print(f"1.5 + 2.5 + 3.0 = {test2}")

test3 = add_three(-5, 15, -10)
print(f"-5 + 15 + (-10) = {test3}")

# Demonstrating that we can use the function in calculations
double_sum = add_three(1, 2, 3) * 2
print(f"Double of (1 + 2 + 3) = {double_sum}")

print("\nFunction practice completed successfully!")
