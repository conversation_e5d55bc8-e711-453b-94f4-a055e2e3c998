"""
Holiday Planning Program - User-Defined Functions Practice

This program demonstrates the use of user-defined functions in Python
through a holiday planning application. It includes functions for:
- Calculating travel costs
- Planning activities
- Managing budgets
- Holiday recommendations

Author: AI Assistant
Date: 2025-09-02
"""

import random


def calculate_flight_cost(destination, passengers=1, class_type="economy"):
    """
    Calculate the cost of flights based on destination and class.
    
    Parameters:
    destination (str): The destination country/city
    passengers (int): Number of passengers (default: 1)
    class_type (str): Flight class - "economy", "business", or "first" (default: "economy")
    
    Returns:
    float: Total flight cost
    """
    
    # Base costs for different destinations
    base_costs = {
        "paris": 800,
        "tokyo": 1200,
        "new york": 600,
        "london": 700,
        "sydney": 1500,
        "dubai": 900,
        "rome": 750,
        "bangkok": 1000
    }
    
    # Class multipliers
    class_multipliers = {
        "economy": 1.0,
        "business": 2.5,
        "first": 4.0
    }
    
    destination_lower = destination.lower()
    
    if destination_lower in base_costs:
        base_cost = base_costs[destination_lower]
    else:
        base_cost = 800  # Default cost for unlisted destinations
    
    class_multiplier = class_multipliers.get(class_type.lower(), 1.0)
    
    total_cost = base_cost * passengers * class_multiplier
    return total_cost


def calculate_accommodation_cost(nights, room_type="standard", location="city"):
    """
    Calculate accommodation costs based on nights, room type, and location.
    
    Parameters:
    nights (int): Number of nights
    room_type (str): Type of room - "budget", "standard", "luxury" (default: "standard")
    location (str): Location type - "city", "beach", "mountain" (default: "city")
    
    Returns:
    float: Total accommodation cost
    """
    
    # Base cost per night for different room types
    room_costs = {
        "budget": 50,
        "standard": 120,
        "luxury": 300
    }
    
    # Location multipliers
    location_multipliers = {
        "city": 1.0,
        "beach": 1.3,
        "mountain": 1.1
    }
    
    base_cost = room_costs.get(room_type.lower(), 120)
    location_multiplier = location_multipliers.get(location.lower(), 1.0)
    
    total_cost = base_cost * nights * location_multiplier
    return total_cost


def suggest_activities(destination, budget_per_day):
    """
    Suggest activities based on destination and daily budget.
    
    Parameters:
    destination (str): The destination
    budget_per_day (float): Daily budget for activities
    
    Returns:
    list: List of suggested activities
    """
    
    activities = {
        "paris": {
            "low": ["Visit free museums", "Walk along Seine", "Explore Montmartre"],
            "medium": ["Eiffel Tower visit", "Louvre Museum", "Seine river cruise"],
            "high": ["Fine dining experience", "Private city tour", "Cabaret show"]
        },
        "tokyo": {
            "low": ["Temple visits", "Public gardens", "Street food tours"],
            "medium": ["Tokyo Skytree", "Sushi making class", "Traditional theater"],
            "high": ["Private geisha dinner", "Helicopter tour", "Luxury spa day"]
        },
        "default": {
            "low": ["Local markets", "Walking tours", "Public parks"],
            "medium": ["Museums", "Local attractions", "Cultural shows"],
            "high": ["Private tours", "Fine dining", "Luxury experiences"]
        }
    }
    
    destination_lower = destination.lower()
    
    if budget_per_day < 50:
        budget_category = "low"
    elif budget_per_day < 150:
        budget_category = "medium"
    else:
        budget_category = "high"
    
    if destination_lower in activities:
        return activities[destination_lower][budget_category]
    else:
        return activities["default"][budget_category]


def calculate_total_budget(destination, nights, passengers=1, room_type="standard", 
                          class_type="economy", daily_activity_budget=100):
    """
    Calculate the total holiday budget.
    
    Parameters:
    destination (str): The destination
    nights (int): Number of nights
    passengers (int): Number of passengers
    room_type (str): Type of accommodation
    class_type (str): Flight class
    daily_activity_budget (float): Daily budget for activities
    
    Returns:
    dict: Breakdown of costs
    """
    
    flight_cost = calculate_flight_cost(destination, passengers, class_type)
    accommodation_cost = calculate_accommodation_cost(nights, room_type)
    activity_cost = daily_activity_budget * nights
    
    # Add 20% for miscellaneous expenses
    misc_cost = (flight_cost + accommodation_cost + activity_cost) * 0.2
    
    total_cost = flight_cost + accommodation_cost + activity_cost + misc_cost
    
    return {
        "flights": flight_cost,
        "accommodation": accommodation_cost,
        "activities": activity_cost,
        "miscellaneous": misc_cost,
        "total": total_cost
    }


def get_holiday_recommendation(budget, preferred_climate="any"):
    """
    Recommend a holiday destination based on budget and climate preference.
    
    Parameters:
    budget (float): Total available budget
    preferred_climate (str): "warm", "cold", "temperate", or "any"
    
    Returns:
    str: Recommended destination
    """
    
    destinations = {
        "warm": ["Dubai", "Bangkok", "Sydney"],
        "cold": ["London", "Paris", "Tokyo"],
        "temperate": ["Rome", "New York"],
        "any": ["Paris", "Tokyo", "New York", "London", "Sydney", "Dubai", "Rome", "Bangkok"]
    }
    
    climate_destinations = destinations.get(preferred_climate.lower(), destinations["any"])
    
    # Filter destinations based on budget (rough estimate)
    affordable_destinations = []
    
    for dest in climate_destinations:
        estimated_cost = calculate_total_budget(dest, 7)["total"]  # 7-night estimate
        if estimated_cost <= budget:
            affordable_destinations.append(dest)
    
    if affordable_destinations:
        return random.choice(affordable_destinations)
    else:
        return "Consider a local destination or increase your budget"


def display_holiday_plan(destination, nights, passengers=1, room_type="standard", 
                        class_type="economy", daily_activity_budget=100):
    """
    Display a complete holiday plan with costs and activities.
    
    Parameters:
    destination (str): The destination
    nights (int): Number of nights
    passengers (int): Number of passengers
    room_type (str): Type of accommodation
    class_type (str): Flight class
    daily_activity_budget (float): Daily budget for activities
    """
    
    print(f"\n{'='*50}")
    print(f"HOLIDAY PLAN FOR {destination.upper()}")
    print(f"{'='*50}")
    
    # Calculate costs
    budget_breakdown = calculate_total_budget(destination, nights, passengers, 
                                            room_type, class_type, daily_activity_budget)
    
    # Display cost breakdown
    print(f"\nCOST BREAKDOWN:")
    print(f"Flights ({passengers} passenger(s), {class_type}): ${budget_breakdown['flights']:.2f}")
    print(f"Accommodation ({nights} nights, {room_type}): ${budget_breakdown['accommodation']:.2f}")
    print(f"Activities ({nights} days): ${budget_breakdown['activities']:.2f}")
    print(f"Miscellaneous (20%): ${budget_breakdown['miscellaneous']:.2f}")
    print(f"TOTAL COST: ${budget_breakdown['total']:.2f}")
    
    # Display suggested activities
    activities = suggest_activities(destination, daily_activity_budget)
    print(f"\nSUGGESTED ACTIVITIES:")
    for i, activity in enumerate(activities, 1):
        print(f"{i}. {activity}")
    
    print(f"\n{'='*50}")


def main():
    """
    Main function to run the holiday planning program.
    """
    
    print("Welcome to the Holiday Planning Program!")
    print("This program uses user-defined functions to help plan your perfect holiday.")
    
    while True:
        print("\nOptions:")
        print("1. Plan a specific holiday")
        print("2. Get a holiday recommendation")
        print("3. Calculate flight costs only")
        print("4. Calculate accommodation costs only")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ")
        
        if choice == "1":
            # Plan a specific holiday
            destination = input("Enter destination: ")
            nights = int(input("Number of nights: "))
            passengers = int(input("Number of passengers: "))
            
            print("\nRoom types: budget, standard, luxury")
            room_type = input("Room type (default: standard): ") or "standard"
            
            print("\nFlight classes: economy, business, first")
            class_type = input("Flight class (default: economy): ") or "economy"
            
            daily_budget = float(input("Daily activity budget (default: 100): ") or "100")
            
            display_holiday_plan(destination, nights, passengers, room_type, class_type, daily_budget)
        
        elif choice == "2":
            # Get recommendation
            budget = float(input("Enter your total budget: $"))
            
            print("\nClimate preferences: warm, cold, temperate, any")
            climate = input("Preferred climate (default: any): ") or "any"
            
            recommendation = get_holiday_recommendation(budget, climate)
            print(f"\nRecommended destination: {recommendation}")
        
        elif choice == "3":
            # Calculate flight costs only
            destination = input("Enter destination: ")
            passengers = int(input("Number of passengers: "))
            
            print("\nFlight classes: economy, business, first")
            class_type = input("Flight class (default: economy): ") or "economy"
            
            cost = calculate_flight_cost(destination, passengers, class_type)
            print(f"\nFlight cost: ${cost:.2f}")
        
        elif choice == "4":
            # Calculate accommodation costs only
            nights = int(input("Number of nights: "))
            
            print("\nRoom types: budget, standard, luxury")
            room_type = input("Room type (default: standard): ") or "standard"
            
            print("\nLocation types: city, beach, mountain")
            location = input("Location type (default: city): ") or "city"
            
            cost = calculate_accommodation_cost(nights, room_type, location)
            print(f"\nAccommodation cost: ${cost:.2f}")
        
        elif choice == "5":
            print("Thank you for using the Holiday Planning Program!")
            break
        
        else:
            print("Invalid choice. Please try again.")


# Run the program if this file is executed directly
if __name__ == "__main__":
    main()
