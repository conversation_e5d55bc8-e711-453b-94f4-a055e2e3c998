{".class": "MypyFile", "_fullname": "augholiday", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "augholiday.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "calculate_accommodation_cost": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nights", "room_type", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.calculate_accommodation_cost", "name": "calculate_accommodation_cost", "type": null}}, "calculate_flight_cost": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["destination", "passengers", "class_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.calculate_flight_cost", "name": "calculate_flight_cost", "type": null}}, "calculate_total_budget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["destination", "nights", "passengers", "room_type", "class_type", "daily_activity_budget"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.calculate_total_budget", "name": "calculate_total_budget", "type": null}}, "display_holiday_plan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["destination", "nights", "passengers", "room_type", "class_type", "daily_activity_budget"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.display_holiday_plan", "name": "display_holiday_plan", "type": null}}, "get_holiday_recommendation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["budget", "preferred_climate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.get_holiday_recommendation", "name": "get_holiday_recommendation", "type": null}}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.main", "name": "main", "type": null}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "suggest_activities": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["destination", "budget_per_day"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "augholiday.suggest_activities", "name": "suggest_activities", "type": null}}}, "path": "C:\\dev\\GN25060018715\\Level 1 - Foundations of AI Engineering\\M02T08 – Programming with User-defined Functions\\augholiday.py"}